2025-07-30 08:10:56 - DEBUG - A0_config-390: 启用离线模式

2025-07-30 08:10:56 - DEBUG - A0_config-618: [调试] 离线模式已启用，跳过VIP检查

2025-07-30 08:10:56 - DEBUG - A0_config-763: [调试] 正在保存平台配置到: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\config\platforms.json

2025-07-30 08:10:56 - DEBUG - A0_config-766: [调试] 平台配置保存成功

2025-07-30 08:10:57 - DEBUG - A0_start_task-35: 基础模块导入成功

2025-07-30 08:10:57 - DEBUG - A0_start_task-236: 192GB内存优化环境变量已设置

2025-07-30 08:10:57 - DEBUG - A30_模型内存管理-27: 🚀 模型内存管理器启动 - 最大缓存: 64.0GB

2025-07-30 08:10:57 - DEBUG - A0_start_task-242: 模型内存管理器已启动

2025-07-30 08:11:12 - DEBUG - A0_start_task-187: 已成功修补A4_working.启动程序函数

2025-07-30 08:11:12 - DEBUG - A0_start_task-190: 批量任务模块修复已准备好

2025-07-30 08:11:12 - DEBUG - A1_注册账号-102: DEBUG: MainWindow __init__ start

2025-07-30 08:11:12 - DEBUG - A1_注册账号-318: 离线模式，跳过用户状态查询。

2025-07-30 08:11:13 - DEBUG - A1_注册账号-182: 检测到离线模式，将直接启动主程序。

2025-07-30 08:11:13 - DEBUG - A0_start_task-216: 跳过更新检查

2025-07-30 08:11:13 - DEBUG - A1_注册账号-230: 已修复: A0_config.字体色

2025-07-30 08:11:13 - DEBUG - main_window-248: 界面加载完成

2025-07-30 08:11:13 - DEBUG - main_window-371: 🔄 浏览器绘图方案未启用，使用默认绘图流程

2025-07-30 08:11:26 - DEBUG - path_manager-240: ℹ️ 配置文件已使用相对路径，无需迁移: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\原创项目\蜂蝶随香\蜂蝶随香.json

2025-07-30 08:11:26 - DEBUG - main_window-2152: [调试] 开始修复所有备选图片数据...

2025-07-30 08:11:26 - DEBUG - main_window-2166: [调试] 所有备选图片数据完整，无需修复

2025-07-30 08:11:26 - DEBUG - main_window-3741: 加载项目任务

2025-07-30 08:11:26 - DEBUG - main_window-307: 已加载角色专用种子配置，包含 3 个角色:

2025-07-30 08:11:26 - DEBUG - main_window-312:   - 艾米丽: seed=1721289378 (禁用, 更新时间: 2025-07-01 09:30:00)

2025-07-30 08:11:26 - DEBUG - main_window-312:   - 露西: seed=2847563921 (禁用, 更新时间: 2025-07-01 09:30:00)

2025-07-30 08:11:26 - DEBUG - main_window-312:   - 斑斑: seed=3956842107 (禁用, 更新时间: 2025-07-01 09:30:00)

2025-07-30 08:11:26 - DEBUG - main_window-3935: 播放任务完成提示音

2025-07-30 08:11:26 - DEBUG - main_window-4997: 加载项目任务完成: 蜂蝶随香

2025-07-30 08:11:29 - DEBUG - 批量任务-44: 用时: 3 秒

2025-07-30 08:11:31 - DEBUG - main_window-3935: 播放任务完成提示音

2025-07-30 08:11:32 - DEBUG - 云端模型管理器-43: [更新] 正在获取Pollinations模型列表...

2025-07-30 08:11:34 - DEBUG - 云端模型管理器-82: [成功] 获取到 3 个Pollinations模型

2025-07-30 08:11:34 - DEBUG - 云端模型管理方法-32: [模型] 已加载 3 个Pollinations模型

2025-07-30 08:11:34 - DEBUG - Pollinations风格分类-187: 风格配置初始化完成

2025-07-30 08:11:34 - DEBUG - 绘图模型按钮-111: ✅ 已使用分类风格系统填充云端风格下拉框

2025-07-30 08:11:34 - DEBUG - Pollinations风格设置辅助方法-72: 当前无风格选择，已清空提示词

2025-07-30 08:11:34 - DEBUG - 工作流管理器-93: ✅ 云端定妆一致性工作流模块加载成功

2025-07-30 08:11:34 - DEBUG - 工作流管理器-100: ✅ 文本驱动角色一致性工作流模块加载成功

2025-07-30 08:11:43 - DEBUG - 工作流管理器-195: ✅ NVIDIA ConsisStory工作流模块加载成功

2025-07-30 08:11:43 - DEBUG - update_background_combo-29: [DEBUG] 设置背景下拉框选中项: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\背景图片\2.jpg

2025-07-30 08:11:43 - DEBUG - 常规设置-132: 🔧 直接创建图床管理器组...

2025-07-30 08:11:43 - DEBUG - 常规设置-138: ✅ 图床管理器组创建成功

2025-07-30 08:11:43 - DEBUG - 常规设置-191: ✅ 图床管理器组直接创建完成

2025-07-30 08:11:43 - DEBUG - 加载推理模板选项-29: [调试] 加载推理模板选项 - 当前模板: 自定义: 4

2025-07-30 08:11:43 - DEBUG - 加载推理模板选项-34: [调试] 推理模板已设置为: 自定义: 4 (索引: 9)

2025-07-30 08:11:43 - DEBUG - A20_翻译管理-369: [翻译管理器] 设置翻译模型为: Qwen/Qwen2.5-7B-Instruct

2025-07-30 08:11:43 - DEBUG - on_translation_model_changed-9: 翻译模型已更改为: Qwen/Qwen2.5-7B-Instruct

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-11: 🔧 开始创建图床管理器组...

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-16: ✅ 图床管理器组基础组件创建成功

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-22: ✅ 主布局创建成功

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-29: ✅ 说明标签创建成功

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-34: ✅ 按钮布局创建成功

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-63: ⚠️ 使用备用按钮事件

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-66: ✅ 图床管理器按钮创建成功

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-79: ⚠️ 使用备用自动同步事件

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-82: ✅ 自动同步选项创建成功

2025-07-30 08:11:43 - DEBUG - 创建图床管理器组-90: 🎉 图床管理器组创建完成！

2025-07-30 08:11:43 - DEBUG - update_background_combo-29: [DEBUG] 设置背景下拉框选中项: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\背景图片\2.jpg

2025-07-30 08:11:43 - DEBUG - 常规设置-132: 🔧 直接创建图床管理器组...

2025-07-30 08:11:43 - DEBUG - 常规设置-138: ✅ 图床管理器组创建成功

2025-07-30 08:11:43 - DEBUG - 常规设置-191: ✅ 图床管理器组直接创建完成

2025-07-30 08:11:43 - DEBUG - 几何设置辅助-22: [窗口] 自动居中显示: (498, 352)

2025-07-30 08:11:51 - DEBUG - closeEvent-7: 🔧 软件设置窗口关闭，自动保存配置...

2025-07-30 08:11:51 - DEBUG - 保存所有配置-110: [DEBUG] 背景图片配置无变化: C:\Users\<USER>\Desktop\纯净wen\wen整合 - 副本 - 副本\项目文件\背景图片\2.jpg

2025-07-30 08:11:51 - DEBUG - 保存所有配置-161: 📋 已保存的配置项:

2025-07-30 08:11:51 - DEBUG - 保存所有配置-162:    GPT平台: 智谱AI

2025-07-30 08:11:51 - DEBUG - 保存所有配置-163:    GPT模型: glm-4-flash-250414

2025-07-30 08:11:51 - DEBUG - 保存所有配置-164:    图片尺寸: 1248x1248

2025-07-30 08:11:51 - DEBUG - 保存所有配置-165:    采样步数: 50

2025-07-30 08:11:51 - DEBUG - closeEvent-9: ✅ 配置保存成功

